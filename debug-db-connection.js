#!/usr/bin/env node

/**
 * Debug script to test database connection from Docker container
 */

const { Pool } = require('pg');
require('dotenv').config();

async function debugConnection() {
  console.log('=== Database Connection Debug ===');
  console.log('Environment variables:');
  console.log(`  PGHOST: ${process.env.PGHOST}`);
  console.log(`  PGPORT: ${process.env.PGPORT}`);
  console.log(`  PGDATABASE: ${process.env.PGDATABASE}`);
  console.log(`  PGUSER: ${process.env.PGUSER}`);
  console.log(`  PGSSLMODE: ${process.env.PGSSLMODE}`);
  console.log('');

  // Test multiple host options for Docker networking
  const hostsToTry = [
    process.env.PGHOST,
    '**********',      // Default Docker bridge gateway
    'host.docker.internal', // Docker Desktop
    '**********',      // Alternative Docker network
    'localhost',       // Direct localhost
    '127.0.0.1'        // Direct IP
  ];

  for (const host of hostsToTry) {
    if (!host) continue;
    
    console.log(`Testing connection to ${host}:${process.env.PGPORT}...`);
    
    const pool = new Pool({
      host: host,
      user: process.env.PGUSER,
      password: process.env.PGPASSWORD,
      database: process.env.PGDATABASE,
      port: parseInt(process.env.PGPORT || '5432'),
      ssl: process.env.PGSSLMODE === 'disable' ? false : { rejectUnauthorized: false },
      connectionTimeoutMillis: 5000, // 5 second timeout
    });

    try {
      const client = await pool.connect();
      const result = await client.query('SELECT NOW() as current_time, current_database() as db_name');
      
      console.log(`✅ SUCCESS: Connected to ${host}`);
      console.log(`   Database: ${result.rows[0].db_name}`);
      console.log(`   Time: ${result.rows[0].current_time}`);
      
      // Test if domains table exists
      try {
        const domainsResult = await client.query('SELECT COUNT(*) as count FROM domains');
        console.log(`   Domains table: ${domainsResult.rows[0].count} records`);
      } catch (error) {
        console.log(`   Domains table: NOT FOUND (${error.message})`);
      }
      
      client.release();
      await pool.end();
      
      console.log(`\n🎉 Working connection found: ${host}`);
      console.log(`Update your .env file with: PGHOST=${host}`);
      return;
      
    } catch (error) {
      console.log(`❌ FAILED: ${host} - ${error.message}`);
      await pool.end();
    }
    
    console.log('');
  }
  
  console.log('❌ No working database connection found');
  console.log('\nTroubleshooting steps:');
  console.log('1. Ensure PostgreSQL is running on the VPS');
  console.log('2. Check PostgreSQL configuration allows connections from Docker network');
  console.log('3. Verify firewall settings');
  console.log('4. Check if PostgreSQL is listening on 0.0.0.0:5432');
}

debugConnection().catch(console.error);
