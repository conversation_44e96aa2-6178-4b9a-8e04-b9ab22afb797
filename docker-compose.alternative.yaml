# Alternative Docker Compose configuration for TempFly.io
# Coolify-compatible version with host networking for PostgreSQL access

services:
  app1:
    build: .
    env_file: .env
    deploy:
      resources:
        limits:
          cpus: "2"
          memory: 3g
    expose:
      - "3000"
    restart: always
    networks:
      - coolify-network
    extra_hosts:
      - "host.docker.internal:host-gateway"
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:3000/healthz"]
      interval: 20s
      timeout: 10s
      retries: 3
      start_period: 30s
    environment:
      - NODE_ENV=production
      - PORT=3000
      - INSTANCE_ID=1
      # Connect to PostgreSQL on VPS host via host.docker.internal
      - PGHOST=host.docker.internal
      - PGPORT=5432
      - PGDATABASE=${PGDATABASE}
      - PGUSER=${PGUSER}
      - PGPASSWORD=${PGPASSWORD}
      - PGSSLMODE=disable
      - DATABASE_URL=postgresql://${PGUSER}:${PGPASSWORD}@host.docker.internal:5432/${PGDATABASE}?sslmode=disable
      - PG_MAX_CONNECTIONS=20
      - PG_MIN_CONNECTIONS=5
      - PG_IDLE_TIMEOUT=20000
      # Redis connection within Docker network
      - REDIS_ENABLED=true
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=${REDIS_PASSWORD}
      - REDIS_DEFAULT_TTL=7200
      - CONNECTION_WARMUP_INTERVAL=45000
      - MEMORY_CACHE_SIZE=200
      - MAX_MEMORY_CACHE_ITEMS=1000
      - API_URL=${API_URL}
      - INTERNAL_API_KEY=${INTERNAL_API_KEY}
      - ENCRYPTION_KEY=${ENCRYPTION_KEY}
      - RAPIDAPI_PROXY_SECRET=${RAPIDAPI_PROXY_SECRET}
    volumes:
      - type: bind
        source: ./logs
        target: /app/logs
    depends_on:
      - redis

  redis:
    image: redis:7-alpine
    deploy:
      resources:
        limits:
          cpus: "1"
          memory: 1g
    volumes:
      - redis_data:/data
      - ./redis.conf:/usr/local/etc/redis/redis.conf
    command: ["redis-server", "/usr/local/etc/redis/redis.conf", "--requirepass", "${REDIS_PASSWORD}"]
    restart: always
    healthcheck:
      test: ["CMD", "redis-cli", "-a", "${REDIS_PASSWORD}", "ping"]
      interval: 30s
      timeout: 5s
      retries: 3
      start_period: 10s
    networks:
      - coolify-network

volumes:
  redis_data:

networks:
  coolify-network:
    external: true
