# Alternative Docker Compose configuration for TempFly.io
# This version uses host networking for the app while keeping Redis in Docker network
# Use this if the main configuration doesn't work

services:
  app1:
    build: .
    env_file: .env
    deploy:
      resources:
        limits:
          cpus: "2"
          memory: 3g
    ports:
      - "3000:3000"
    restart: always
    # Remove host networking for Coolify compatibility
    networks:
      - default
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:3000/healthz"]
      interval: 20s
      timeout: 10s
      retries: 3
      start_period: 30s
    environment:
      - NODE_ENV=production
      - PORT=3000
      - INSTANCE_ID=1
      # Connect to PostgreSQL on VPS host IP (since host.docker.internal may not work)
      - PGHOST=************
      - PGPORT=5432
      - PGDATABASE=${PGDATABASE}
      - PGUSER=${PGUSER}
      - PGPASSWORD=${PGPASSWORD}
      - PGSSLMODE=disable
      - DATABASE_URL=postgresql://${PGUSER}:${PGPASSWORD}@************:5432/${PGDATABASE}?sslmode=disable
      - PG_MAX_CONNECTIONS=20
      - PG_MIN_CONNECTIONS=5
      - PG_IDLE_TIMEOUT=20000
      # Redis connection within Docker network
      - REDIS_ENABLED=true
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=${REDIS_PASSWORD}
      - REDIS_DEFAULT_TTL=7200
      - CONNECTION_WARMUP_INTERVAL=45000
      - MEMORY_CACHE_SIZE=200
      - MAX_MEMORY_CACHE_ITEMS=1000
      - API_URL=${API_URL}
      - INTERNAL_API_KEY=${INTERNAL_API_KEY}
      - ENCRYPTION_KEY=${ENCRYPTION_KEY}
      - RAPIDAPI_PROXY_SECRET=${RAPIDAPI_PROXY_SECRET}
    volumes:
      - type: bind
        source: ./logs
        target: /app/logs
    depends_on:
      - redis
    networks:
      - app-network
    extra_hosts:
      - "host.docker.internal:host-gateway"

  redis:
    image: redis:7-alpine
    deploy:
      resources:
        limits:
          cpus: "1"
          memory: 1g
    # Remove port exposure - Redis only accessible within Docker network
    volumes:
      - redis_data:/data
      - ./redis.conf:/usr/local/etc/redis/redis.conf
    command: ["redis-server", "/usr/local/etc/redis/redis.conf", "--requirepass", "${REDIS_PASSWORD}"]
    restart: always
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "ping"]
      interval: 30s
      timeout: 5s
      retries: 3
      start_period: 10s
    networks:
      - app-network

volumes:
  redis_data:

networks:
  app-network:
    driver: bridge
