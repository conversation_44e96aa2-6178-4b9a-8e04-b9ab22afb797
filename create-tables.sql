-- TempFly.io Database Schema Creation
-- Run this on your PostgreSQL database to create the required tables

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- Create domains table
CREATE TABLE IF NOT EXISTS domains (
    id BIGSERIAL PRIMARY KEY,
    server_id SMALLINT NOT NULL DEFAULT 1,
    domain VARCHAR(255) NOT NULL UNIQUE,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    is_custom BOOLEAN DEFAULT false,
    mx_record VARCHAR(255),
    spf_record TEXT,
    dkim_record TEXT,
    dmarc_record TEXT,
    verification_status VARCHAR(50) DEFAULT 'pending',
    last_verified_at TIMESTAMP WITH TIME ZONE,
    smtp_enabled BOOLEAN DEFAULT false,
    smtp_server VARCHAR(255),
    smtp_port INTEGER DEFAULT 25,
    smtp_username VARCHAR(255),
    smtp_password VARCHAR(255),
    smtp_secure BOOLEAN DEFAULT false
);

-- Create inboxes table
CREATE TABLE IF NOT EXISTS inboxes (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    username VARCHAR(255),
    domain VARCHAR(255) NOT NULL,
    address VARCHAR(320) NOT NULL UNIQUE,
    email VARCHAR(320),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    expiry_date TIMESTAMP WITH TIME ZONE,
    expires_at TIMESTAMP WITH TIME ZONE,
    is_active BOOLEAN DEFAULT true,
    api_key_id UUID,
    rapidapi_key VARCHAR(255),
    forwarding_enabled BOOLEAN DEFAULT false,
    forwarding_email VARCHAR(320),
    forwarding_verified BOOLEAN DEFAULT false,
    verification_token UUID DEFAULT uuid_generate_v4(),
    last_activity_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create emails table
CREATE TABLE IF NOT EXISTS emails (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    inbox_id UUID NOT NULL REFERENCES inboxes(id) ON DELETE CASCADE,
    from_address VARCHAR(320) NOT NULL,
    from_name VARCHAR(255),
    subject TEXT,
    text_body TEXT,
    text_content TEXT,
    html_body TEXT,
    html_content TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    received_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    read_at TIMESTAMP WITH TIME ZONE,
    headers JSONB,
    to_address VARCHAR(320),
    cc_address TEXT,
    bcc_address TEXT,
    reply_to VARCHAR(320),
    message_id VARCHAR(255),
    in_reply_to VARCHAR(255),
    references_field TEXT,
    priority VARCHAR(50),
    spam_score FLOAT,
    is_spam BOOLEAN DEFAULT false,
    is_deleted BOOLEAN DEFAULT false,
    source TEXT,
    raw_size INTEGER,
    has_attachments BOOLEAN DEFAULT false
);

-- Create other necessary tables
CREATE TABLE IF NOT EXISTS attachments (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    email_id UUID NOT NULL REFERENCES emails(id) ON DELETE CASCADE,
    filename VARCHAR(255) NOT NULL,
    content_type VARCHAR(255),
    content_id VARCHAR(255),
    size INTEGER,
    content BYTEA,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    storage_path TEXT,
    is_inline BOOLEAN DEFAULT false
);

CREATE TABLE IF NOT EXISTS api_keys (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    key VARCHAR(255) NOT NULL UNIQUE,
    name VARCHAR(255),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP WITH TIME ZONE,
    is_active BOOLEAN DEFAULT true,
    last_used_at TIMESTAMP WITH TIME ZONE,
    usage_count INTEGER DEFAULT 0,
    rate_limit INTEGER,
    permissions JSONB
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_inboxes_address ON inboxes(address);
CREATE INDEX IF NOT EXISTS idx_inboxes_domain ON inboxes(domain);
CREATE INDEX IF NOT EXISTS idx_inboxes_rapidapi_key ON inboxes(rapidapi_key);
CREATE INDEX IF NOT EXISTS idx_emails_inbox_id ON emails(inbox_id);
CREATE INDEX IF NOT EXISTS idx_emails_created_at ON emails(created_at);
CREATE INDEX IF NOT EXISTS idx_domains_domain ON domains(domain);
CREATE INDEX IF NOT EXISTS idx_domains_is_active ON domains(is_active);

-- Insert default domains
INSERT INTO domains (domain, is_active, is_custom, smtp_enabled)
VALUES
    ('boxqix.com', true, false, true),
    ('fitnessfocusgym.site', true, false, true),
    ('gourmetguidecafe.site', true, false, true),
    ('greenthumbgardens.store', true, false, true),
    ('joibcddsd.online', true, false, true),
    ('mailvu.space', true, false, true),
    ('probox.site', true, false, true),
    ('rubymail.site', true, false, true),
    ('trendygadgetshop.store', true, false, true),
    ('umorjjsassaas.store', true, false, true),
    ('umorjjsdsd.site', true, false, true),
    ('wellnesswavecenter.site', true, false, true)
ON CONFLICT (domain) DO NOTHING;

-- Verify tables were created
SELECT 
    schemaname,
    tablename,
    tableowner
FROM pg_tables 
WHERE schemaname = 'public' 
ORDER BY tablename;
